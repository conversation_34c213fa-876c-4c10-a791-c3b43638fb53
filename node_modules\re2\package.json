{"name": "re2", "version": "1.22.1", "description": "Bindings for RE2: fast, safe alternative to backtracking regular expression engines.", "homepage": "https://github.com/uhop/node-re2", "bugs": "https://github.com/uhop/node-re2/issues", "type": "commonjs", "main": "re2.js", "types": "re2.d.ts", "files": ["binding.gyp", "lib", "re2.d.ts", "scripts/*.js", "vendor"], "dependencies": {"install-artifact-from-github": "^1.4.0", "nan": "^2.22.2", "node-gyp": "^11.2.0"}, "devDependencies": {"@types/node": "^22.15.17", "heya-unit": "^0.3.0", "typescript": "^5.8.3"}, "scripts": {"test": "node tests/tests.js", "ts-test": "tsc", "save-to-github": "save-to-github-cache --artifact build/Release/re2.node", "install": "install-from-cache --artifact build/Release/re2.node --host-var RE2_DOWNLOAD_MIRROR --skip-path-var RE2_DOWNLOAD_SKIP_PATH --skip-ver-var RE2_DOWNLOAD_SKIP_VER || node-gyp -j max rebuild", "verify-build": "node scripts/verify-build.js", "build:dev": "node-gyp -j max build --debug", "build": "node-gyp -j max build", "rebuild:dev": "node-gyp -j max rebuild --debug", "rebuild": "node-gyp -j max rebuild", "clean": "node-gyp clean && node-gyp configure", "clean-build": "node-gyp clean"}, "github": "https://github.com/uhop/node-re2", "repository": {"type": "git", "url": "git://github.com/uhop/node-re2.git"}, "keywords": ["RegExp", "RegEx", "text processing", "PCRE alternative"], "author": "<PERSON> <<EMAIL>> (https://lazutkin.com/)", "license": "BSD-3-<PERSON><PERSON>"}