#!/usr/bin/env node

const requireAuth = require('firebase-tools/lib/requireAuth');
const api = require('firebase-tools/lib/api');
const fs = require('fs-extra');
const https = require('https');
const http = require('http');
const { URL } = require('url');

if (!process.argv[2]) {
    console.error(`
ERROR: Must supply a site name. Usage:

  node fetchFiles-modern.js <site_name>`);
  process.exit(1);
}

const site = process.argv[2];

async function getLatestVersionName() {
  const result = await api.request('GET', `/v1beta1/sites/${site}/releases?pageSize=1`, {
    auth: true,
    origin: api.hostingApiOrigin,
  });
  const release = (result.body.releases || [])[0];
  if (release) {
    return release.version.name;
  }
  return null;
}

const LIST_PAGE_SIZE = 1000;
async function listFiles(versionName, existing = [], pageToken = null) {
  const result = await api.request('GET', `/v1beta1/${versionName}/files?pageSize=${LIST_PAGE_SIZE}${pageToken ? `&pageToken=${pageToken}` : ''}`, {auth: true, origin: api.hostingApiOrigin});
  result.body.files.forEach(file => existing.push(file.path));
  if (result.body.nextPageToken) {
    return await listFiles(versionName, existing, result.body.nextPageToken);
  }
  return existing;
}

function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    fs.ensureFileSync(filePath);
    const file = fs.createWriteStream(filePath);
    
    client.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

const MAX_CONCURRENT_DOWNLOADS = 10;

(async function() {
  try {
    console.log('Authenticating with Firebase...');
    await requireAuth({}, ['https://www.googleapis.com/auth/cloud-platform']);
    
    console.log('Getting latest version...');
    const v = await getLatestVersionName();
    if (!v) {
      console.error('No releases found for site:', site);
      process.exit(1);
    }
    
    const vid = v.split('/')[v.split('/').length - 1];
    console.log('Version ID:', vid);
    
    console.log('Listing files...');
    const toFetch = await listFiles(v);
    console.log(`Found ${toFetch.length} files to download`);
    
    const dirName = `${site}_${vid}`;
    console.log(`Downloading to directory: ${dirName}`);
    
    let completed = 0;
    let inProgress = 0;
    
    async function downloadNext() {
      if (toFetch.length === 0) {
        return;
      }
      
      const filePath = toFetch.shift();
      const url = `https://${site}.firebaseapp.com${filePath}`;
      const localPath = `${dirName}${filePath}`;
      
      inProgress++;
      console.log(`[${completed + 1}/${completed + toFetch.length + inProgress}] Downloading: ${filePath}`);
      
      try {
        await downloadFile(url, localPath);
        completed++;
        console.log(`✓ Downloaded: ${filePath}`);
      } catch (error) {
        console.error(`✗ Failed to download ${filePath}:`, error.message);
      }
      
      inProgress--;
      
      // Continue downloading if there are more files
      if (toFetch.length > 0) {
        await downloadNext();
      }
    }
    
    // Start concurrent downloads
    const promises = [];
    for (let i = 0; i < Math.min(MAX_CONCURRENT_DOWNLOADS, toFetch.length); i++) {
      promises.push(downloadNext());
    }
    
    await Promise.all(promises);
    
    console.log();
    console.log(`✅ Complete! Downloaded ${completed} files to ${dirName}/`);
    
  } catch (e) {
    console.error("ERROR:", e.stack);
    process.exit(1);
  }
})();
