# C++ Upgrade Tools

Abseil may occasionally release API-breaking changes. As noted in our
[Compatibility Guidelines][compatibility-guide], we will aim to provide a tool
to do the work of effecting such API-breaking changes, when absolutely
necessary.

These tools will be listed on the [C++ Upgrade Tools][upgrade-tools] guide on
https://abseil.io.

For more information, the [C++ Automated Upgrade Guide][api-upgrades-guide]
outlines this process.

[compatibility-guide]: https://abseil.io/about/compatibility
[api-upgrades-guide]: https://abseil.io/docs/cpp/tools/api-upgrades
[upgrade-tools]: https://abseil.io/docs/cpp/tools/upgrades/

