#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const { URL } = require('url');

// Check if puppeteer is available
let puppeteer;
try {
  puppeteer = require('puppeteer');
} catch (error) {
  console.error(`❌ Puppeteer is not installed. Please install it first:
  npm install puppeteer
  
Or use the lightweight version:
  npm install puppeteer-core`);
  process.exit(1);
}

if (!process.argv[2]) {
    console.error(`
ERROR: Must supply a site URL. Usage:

  node browser-automation-scraper.js <site_url> [options]
  
Examples:
  node browser-automation-scraper.js https://my-site.firebaseapp.com
  node browser-automation-scraper.js https://my-site.web.app --spa
  node browser-automation-scraper.js https://my-site.com --wait=5000

Options:
  --spa               Enable SPA (Single Page Application) mode
  --wait=<ms>         Wait time for dynamic content (default: 3000ms)
  --screenshots       Take screenshots of pages
  --mobile            Use mobile viewport
  --headless=false    Run browser in visible mode
  --timeout=<ms>      Page load timeout (default: 30000ms)
`);
  process.exit(1);
}

const siteUrl = process.argv[2].replace(/\/$/, '');
const siteName = new URL(siteUrl).hostname.replace(/\./g, '_');

const options = {
  spa: false,
  wait: 3000,
  screenshots: false,
  mobile: false,
  headless: true,
  timeout: 30000
};

// Parse command line options
for (let i = 3; i < process.argv.length; i++) {
  const arg = process.argv[i];
  if (arg === '--spa') {
    options.spa = true;
  } else if (arg.startsWith('--wait=')) {
    options.wait = parseInt(arg.split('=')[1]) || 3000;
  } else if (arg === '--screenshots') {
    options.screenshots = true;
  } else if (arg === '--mobile') {
    options.mobile = true;
  } else if (arg === '--headless=false') {
    options.headless = false;
  } else if (arg.startsWith('--timeout=')) {
    options.timeout = parseInt(arg.split('=')[1]) || 30000;
  }
}

const outputDir = `${siteName}_browser_scrape_${Date.now()}`;
const discoveredUrls = new Set();
const scrapedPages = new Set();
const networkRequests = new Map();
const errors = [];

console.log(`🤖 Browser Automation Scraper`);
console.log(`🌐 Site: ${siteUrl}`);
console.log(`📂 Output: ${outputDir}`);
console.log(`⚙️  Options:`, options);
console.log('\n' + '='.repeat(50) + '\n');

// Ensure output directory exists
fs.ensureDirSync(outputDir);
fs.ensureDirSync(path.join(outputDir, 'pages'));
fs.ensureDirSync(path.join(outputDir, 'assets'));
if (options.screenshots) {
  fs.ensureDirSync(path.join(outputDir, 'screenshots'));
}

// Common SPA routes to try
const spaRoutes = [
  '/',
  '/home',
  '/about',
  '/contact',
  '/login',
  '/register',
  '/dashboard',
  '/profile',
  '/settings',
  '/help',
  '/faq',
  '/terms',
  '/privacy',
  '/blog',
  '/news',
  '/products',
  '/services',
  '/portfolio',
  '/gallery'
];

// Save network request to file
async function saveNetworkResource(url, response) {
  try {
    const parsedUrl = new URL(url);
    const pathname = parsedUrl.pathname;
    
    // Skip data URLs and external resources
    if (url.startsWith('data:') || !url.startsWith(siteUrl)) {
      return;
    }
    
    // Determine file path
    let filePath;
    if (pathname === '/' || pathname === '') {
      filePath = path.join(outputDir, 'pages', 'index.html');
    } else {
      const cleanPath = pathname.startsWith('/') ? pathname.substring(1) : pathname;
      filePath = path.join(outputDir, 'assets', cleanPath);
    }
    
    // Ensure directory exists
    fs.ensureDirSync(path.dirname(filePath));
    
    // Get response buffer
    const buffer = await response.buffer();
    
    // Save file
    fs.writeFileSync(filePath, buffer);
    
    const size = Math.round(buffer.length / 1024);
    console.log(`💾 Saved network resource: ${pathname} (${size}KB)`);
    
    return filePath;
  } catch (error) {
    console.log(`⚠️  Failed to save network resource ${url}:`, error.message);
    return null;
  }
}

// Extract links from page content
function extractLinksFromPage(content, baseUrl) {
  const links = new Set();
  
  // Script tags
  const scriptRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
  let match;
  while ((match = scriptRegex.exec(content)) !== null) {
    links.add(match[1]);
  }
  
  // Link tags (CSS, etc.)
  const linkRegex = /<link[^>]*href=["']([^"']+)["'][^>]*>/gi;
  while ((match = linkRegex.exec(content)) !== null) {
    links.add(match[1]);
  }
  
  // Image tags
  const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*>/gi;
  while ((match = imgRegex.exec(content)) !== null) {
    links.add(match[1]);
  }
  
  // Anchor tags for SPA routes
  const anchorRegex = /<a[^>]*href=["']([^"']+)["'][^>]*>/gi;
  while ((match = anchorRegex.exec(content)) !== null) {
    const href = match[1];
    if (!href.startsWith('#') && !href.startsWith('mailto:') && !href.startsWith('tel:') && !href.startsWith('http')) {
      links.add(href);
    }
  }
  
  // Convert relative URLs to absolute
  return Array.from(links).map(link => {
    if (link.startsWith('http')) return link;
    if (link.startsWith('//')) return 'https:' + link;
    if (link.startsWith('/')) return baseUrl + link;
    return baseUrl + '/' + link;
  }).filter(link => {
    try {
      const url = new URL(link);
      return url.hostname === new URL(baseUrl).hostname;
    } catch {
      return false;
    }
  });
}

// Scrape a single page
async function scrapePage(page, url, routePath = '/') {
  try {
    console.log(`🔍 Scraping: ${routePath}`);
    
    // Set up network request interception
    const requestPromises = [];
    
    page.on('response', async (response) => {
      const responseUrl = response.url();
      if (responseUrl.startsWith(siteUrl) && response.status() === 200) {
        networkRequests.set(responseUrl, {
          url: responseUrl,
          status: response.status(),
          contentType: response.headers()['content-type'],
          size: response.headers()['content-length']
        });
        
        // Save important resources
        const contentType = response.headers()['content-type'] || '';
        if (contentType.includes('javascript') || 
            contentType.includes('css') || 
            contentType.includes('json') ||
            contentType.includes('html')) {
          requestPromises.push(saveNetworkResource(responseUrl, response));
        }
      }
    });
    
    // Navigate to page
    await page.goto(url, { 
      waitUntil: 'networkidle2', 
      timeout: options.timeout 
    });
    
    // Wait for dynamic content
    await page.waitForTimeout(options.wait);
    
    // Get page content
    const content = await page.content();
    const title = await page.title();
    
    // Save page HTML
    const pageFileName = routePath === '/' ? 'index.html' : `${routePath.replace(/\//g, '_')}.html`;
    const pageFilePath = path.join(outputDir, 'pages', pageFileName);
    fs.writeFileSync(pageFilePath, content);
    
    console.log(`📄 Saved page: ${pageFileName} (${title})`);
    
    // Take screenshot if enabled
    if (options.screenshots) {
      const screenshotPath = path.join(outputDir, 'screenshots', `${pageFileName.replace('.html', '.png')}`);
      await page.screenshot({ 
        path: screenshotPath, 
        fullPage: true 
      });
      console.log(`📸 Screenshot saved: ${path.basename(screenshotPath)}`);
    }
    
    // Extract links for further discovery
    const discoveredLinks = extractLinksFromPage(content, siteUrl);
    discoveredLinks.forEach(link => {
      if (link.startsWith(siteUrl)) {
        const relativePath = link.replace(siteUrl, '') || '/';
        discoveredUrls.add(relativePath);
      }
    });
    
    // Wait for all network requests to complete
    await Promise.allSettled(requestPromises);
    
    scrapedPages.add(routePath);
    console.log(`✅ Completed scraping: ${routePath}`);
    
    return {
      url,
      routePath,
      title,
      content,
      discoveredLinks: discoveredLinks.length
    };
    
  } catch (error) {
    console.log(`❌ Failed to scrape ${routePath}:`, error.message);
    errors.push({ url, routePath, error: error.message });
    return null;
  }
}

// Main scraping function
async function scrapeWebsite() {
  let browser;
  
  try {
    console.log('🚀 Launching browser...');
    
    // Launch browser
    browser = await puppeteer.launch({
      headless: options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    
    const page = await browser.newPage();
    
    // Set viewport
    if (options.mobile) {
      await page.setViewport({ width: 375, height: 667, isMobile: true });
      await page.setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1');
    } else {
      await page.setViewport({ width: 1920, height: 1080 });
    }
    
    // Enable request interception
    await page.setRequestInterception(true);
    
    page.on('request', (request) => {
      // Allow all requests
      request.continue();
    });
    
    // Handle console messages
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`🔴 Console error: ${msg.text()}`);
      }
    });
    
    // Start with main page
    console.log('\n📄 Scraping main page...');
    await scrapePage(page, siteUrl, '/');
    
    // If SPA mode, try common routes
    if (options.spa) {
      console.log('\n🔄 SPA mode: Trying common routes...');
      
      for (const route of spaRoutes) {
        if (route !== '/' && !scrapedPages.has(route)) {
          const routeUrl = siteUrl + route;
          await scrapePage(page, routeUrl, route);
          
          // Small delay between requests
          await page.waitForTimeout(1000);
        }
      }
      
      // Try discovered URLs
      console.log('\n🔗 Trying discovered URLs...');
      const discoveredArray = Array.from(discoveredUrls).slice(0, 20); // Limit to prevent infinite loops
      
      for (const discoveredPath of discoveredArray) {
        if (!scrapedPages.has(discoveredPath)) {
          const discoveredUrl = siteUrl + (discoveredPath === '/' ? '' : discoveredPath);
          await scrapePage(page, discoveredUrl, discoveredPath);
          
          await page.waitForTimeout(1000);
        }
      }
    }
    
    console.log('\n🔍 Analyzing JavaScript for additional routes...');
    
    // Try to find routes in JavaScript files
    try {
      const jsContent = await page.evaluate(() => {
        const scripts = Array.from(document.querySelectorAll('script'));
        return scripts.map(script => script.innerHTML).join('\n');
      });
      
      // Look for route patterns in JavaScript
      const routePatterns = [
        /['"`]\/[\w\/-]+['"`]/g,
        /path\s*:\s*['"`]([^'"` ]+)['"`]/g,
        /route\s*:\s*['"`]([^'"` ]+)['"`]/g,
        /\{\s*path\s*:\s*['"`]([^'"` ]+)['"`]/g
      ];
      
      const foundRoutes = new Set();
      routePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(jsContent)) !== null) {
          const route = match[1] || match[0].replace(/['"`]/g, '');
          if (route.startsWith('/') && route.length > 1 && !route.includes(' ')) {
            foundRoutes.add(route);
          }
        }
      });
      
      console.log(`🎯 Found ${foundRoutes.size} potential routes in JavaScript`);
      
      // Try the found routes
      for (const route of Array.from(foundRoutes).slice(0, 10)) {
        if (!scrapedPages.has(route)) {
          const routeUrl = siteUrl + route;
          await scrapePage(page, routeUrl, route);
          await page.waitForTimeout(1000);
        }
      }
    } catch (error) {
      console.log('⚠️  Could not analyze JavaScript for routes:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Browser scraping failed:', error.stack);
    errors.push({ error: error.message, stage: 'browser_launch' });
  } finally {
    if (browser) {
      await browser.close();
      console.log('🔒 Browser closed');
    }
  }
  
  // Generate comprehensive report
  console.log('\n' + '='.repeat(50));
  console.log('🎉 BROWSER SCRAPING COMPLETE!');
  console.log(`📊 Statistics:`);
  console.log(`   📄 Pages scraped: ${scrapedPages.size}`);
  console.log(`   🔗 URLs discovered: ${discoveredUrls.size}`);
  console.log(`   🌐 Network requests: ${networkRequests.size}`);
  console.log(`   ❌ Errors: ${errors.length}`);
  console.log(`   📁 Output directory: ${outputDir}`);
  
  // Create detailed report
  const report = {
    scrapingDate: new Date().toISOString(),
    siteUrl,
    options,
    statistics: {
      pagesScraped: scrapedPages.size,
      urlsDiscovered: discoveredUrls.size,
      networkRequests: networkRequests.size,
      errors: errors.length
    },
    scrapedPages: Array.from(scrapedPages),
    discoveredUrls: Array.from(discoveredUrls),
    networkRequests: Array.from(networkRequests.values()),
    errors: errors
  };
  
  fs.writeFileSync(
    path.join(outputDir, 'browser-scraping-report.json'), 
    JSON.stringify(report, null, 2)
  );
  
  // Create index of all scraped content
  const indexHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scraped Content Index - ${siteName}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .file-list { list-style: none; padding: 0; }
        .file-list li { padding: 8px; border-bottom: 1px solid #eee; }
        .file-list a { text-decoration: none; color: #0066cc; }
        .file-list a:hover { text-decoration: underline; }
        .stats { display: flex; gap: 20px; flex-wrap: wrap; }
        .stat { background: #e3f2fd; padding: 15px; border-radius: 5px; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Browser Scraping Results</h1>
        <p><strong>Site:</strong> ${siteUrl}</p>
        <p><strong>Scraped on:</strong> ${new Date().toLocaleString()}</p>
    </div>
    
    <div class="stats">
        <div class="stat">
            <h3>${scrapedPages.size}</h3>
            <p>Pages Scraped</p>
        </div>
        <div class="stat">
            <h3>${discoveredUrls.size}</h3>
            <p>URLs Discovered</p>
        </div>
        <div class="stat">
            <h3>${networkRequests.size}</h3>
            <p>Network Requests</p>
        </div>
    </div>
    
    <div class="section">
        <h2>📄 Scraped Pages</h2>
        <ul class="file-list">
            ${Array.from(scrapedPages).map(page => {
              const fileName = page === '/' ? 'index.html' : `${page.replace(/\//g, '_')}.html`;
              return `<li><a href="pages/${fileName}">${page}</a></li>`;
            }).join('')}
        </ul>
    </div>
    
    ${options.screenshots ? `
    <div class="section">
        <h2>📸 Screenshots</h2>
        <ul class="file-list">
            ${Array.from(scrapedPages).map(page => {
              const fileName = page === '/' ? 'index.png' : `${page.replace(/\//g, '_')}.png`;
              return `<li><a href="screenshots/${fileName}">${page}</a></li>`;
            }).join('')}
        </ul>
    </div>
    ` : ''}
    
    <div class="section">
        <h2>🔗 Network Resources</h2>
        <ul class="file-list">
            ${Array.from(networkRequests.values()).map(req => {
              const path = new URL(req.url).pathname;
              return `<li><a href="assets${path}">${path}</a> <small>(${req.contentType})</small></li>`;
            }).join('')}
        </ul>
    </div>
</body>
</html>
  `;
  
  fs.writeFileSync(path.join(outputDir, 'index.html'), indexHtml);
  
  console.log('\n📄 Detailed report saved to browser-scraping-report.json');
  console.log('🏠 Index page created at index.html');
  
  if (scrapedPages.size > 0) {
    console.log('\n✅ Browser scraping completed successfully!');
    console.log('📁 Check the output directory for all scraped content.');
  } else {
    console.log('\n⚠️  No pages were successfully scraped.');
    console.log('💡 Try adjusting the wait time or checking the site URL.');
  }
}

// Run the browser scraper
scrapeWebsite();