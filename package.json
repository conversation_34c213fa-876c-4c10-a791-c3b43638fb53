{"name": "firebase-code-recovery-suite", "version": "1.0.0", "description": "Comprehensive Firebase code recovery tools with advanced web scraping capabilities", "bin": {"firebase-recover": "./comprehensive-firebase-recovery.js", "web-scrape": "./advanced-web-scraper.js", "browser-scrape": "./browser-automation-scraper.js", "fetch-files": "./fetchFiles.js", "fetch-modern": "./fetchFiles-modern.js", "simple-fetch": "./simple-fetch.js"}, "scripts": {"recover": "node comprehensive-firebase-recovery.js", "scrape": "node advanced-web-scraper.js", "browser": "node browser-automation-scraper.js", "install-puppeteer": "npm install puppeteer"}, "dependencies": {"firebase-tools": "^12.0.0", "fs-extra": "^11.0.0", "request": "^2.88.2"}, "optionalDependencies": {"puppeteer": "^21.0.0"}, "devDependencies": {"puppeteer-core": "^21.0.0"}, "keywords": ["firebase", "code-recovery", "web-scraping", "browser-automation", "lost-code", "website-backup"], "author": "Firebase Recovery Suite", "license": "MIT"}