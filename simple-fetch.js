#!/usr/bin/env node

const https = require('https');
const fs = require('fs-extra');
const path = require('path');

if (!process.argv[2]) {
    console.error(`
ERROR: Must supply a site URL. Usage:

  node simple-fetch.js <site_url>
  
Example:
  node simple-fetch.js https://your-site.firebaseapp.com
  node simple-fetch.js https://your-site.web.app`);
  process.exit(1);
}

const siteUrl = process.argv[2].replace(/\/$/, ''); // Remove trailing slash
const siteName = new URL(siteUrl).hostname.replace(/\./g, '_');

// Common file paths to try downloading
const commonFiles = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/static/js/main.js',
  '/static/css/main.css',
  '/js/main.js',
  '/css/main.css',
  '/css/style.css',
  '/js/script.js',
  '/assets/index.js',
  '/assets/index.css',
  '/_next/static/chunks/main.js',
  '/_next/static/css/app.css',
  '/build/static/js/main.js',
  '/build/static/css/main.css'
];

function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    fs.ensureFileSync(filePath);
    const file = fs.createWriteStream(filePath);
    
    https.get(url, (response) => {
      if (response.statusCode === 404) {
        fs.unlinkSync(filePath);
        resolve(false); // File doesn't exist
        return;
      }
      
      if (response.statusCode !== 200) {
        fs.unlinkSync(filePath);
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve(true);
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {});
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

(async function() {
  try {
    const dirName = `${siteName}_simple_fetch`;
    console.log(`Downloading common files from ${siteUrl} to ${dirName}/`);
    
    let downloaded = 0;
    
    for (const filePath of commonFiles) {
      const url = `${siteUrl}${filePath}`;
      let localPath = `${dirName}${filePath}`;
      
      // Handle root path
      if (filePath === '/') {
        localPath = `${dirName}/index.html`;
      }
      
      console.log(`Trying: ${filePath}`);
      
      try {
        const success = await downloadFile(url, localPath);
        if (success) {
          console.log(`✓ Downloaded: ${filePath}`);
          downloaded++;
        } else {
          console.log(`- Not found: ${filePath}`);
        }
      } catch (error) {
        console.log(`✗ Error downloading ${filePath}:`, error.message);
      }
    }
    
    console.log();
    console.log(`✅ Downloaded ${downloaded} files to ${dirName}/`);
    console.log();
    console.log('To find more files, you can:');
    console.log('1. Check the downloaded HTML files for script/link tags');
    console.log('2. Use browser dev tools to see network requests');
    console.log('3. Use the Firebase CLI method for complete file listing');
    
  } catch (e) {
    console.error("ERROR:", e.stack);
    process.exit(1);
  }
})();
