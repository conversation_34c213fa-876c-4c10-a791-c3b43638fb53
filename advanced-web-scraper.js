#!/usr/bin/env node

const https = require('https');
const http = require('http');
const fs = require('fs-extra');
const path = require('path');
const { URL } = require('url');

if (!process.argv[2]) {
    console.error(`
ERROR: Must supply a site URL. Usage:

  node advanced-web-scraper.js <site_url> [options]
  
Examples:
  node advanced-web-scraper.js https://my-site.firebaseapp.com
  node advanced-web-scraper.js https://my-site.web.app --deep
  node advanced-web-scraper.js https://my-site.com --patterns=react,vue

Options:
  --deep              Enable deep discovery mode
  --patterns=<list>   Specify framework patterns (react,vue,angular,next,nuxt)
  --max-depth=<n>     Maximum crawl depth (default: 3)
  --include-assets    Download images and other assets
  --follow-redirects  Follow HTTP redirects
`);
  process.exit(1);
}

const siteUrl = process.argv[2].replace(/\/$/, '');
const siteName = new URL(siteUrl).hostname.replace(/\./g, '_');

const options = {
  deep: false,
  patterns: ['generic'],
  maxDepth: 3,
  includeAssets: false,
  followRedirects: true
};

// Parse command line options
for (let i = 3; i < process.argv.length; i++) {
  const arg = process.argv[i];
  if (arg === '--deep') {
    options.deep = true;
  } else if (arg.startsWith('--patterns=')) {
    options.patterns = arg.split('=')[1].split(',');
  } else if (arg.startsWith('--max-depth=')) {
    options.maxDepth = parseInt(arg.split('=')[1]) || 3;
  } else if (arg === '--include-assets') {
    options.includeAssets = true;
  } else if (arg === '--follow-redirects') {
    options.followRedirects = true;
  }
}

const outputDir = `${siteName}_advanced_scrape_${Date.now()}`;
const discoveredUrls = new Set();
const downloadedFiles = new Set();
const failedUrls = new Set();

console.log(`🕷️  Advanced Web Scraper`);
console.log(`🌐 Site: ${siteUrl}`);
console.log(`📂 Output: ${outputDir}`);
console.log(`⚙️  Options:`, options);
console.log('\n' + '='.repeat(50) + '\n');

// Framework-specific file patterns
const frameworkPatterns = {
  react: [
    '/static/js/main.*.js',
    '/static/js/runtime-main.*.js',
    '/static/js/[0-9]*.*.chunk.js',
    '/static/css/main.*.css',
    '/static/css/[0-9]*.*.chunk.css',
    '/static/media/*',
    '/manifest.json',
    '/favicon.ico',
    '/logo192.png',
    '/logo512.png'
  ],
  vue: [
    '/js/app.*.js',
    '/js/chunk-vendors.*.js',
    '/js/chunk-common.*.js',
    '/css/app.*.css',
    '/css/chunk-vendors.*.css',
    '/img/*',
    '/fonts/*'
  ],
  angular: [
    '/main.*.js',
    '/polyfills.*.js',
    '/runtime.*.js',
    '/vendor.*.js',
    '/styles.*.css',
    '/assets/*'
  ],
  next: [
    '/_next/static/chunks/main-*.js',
    '/_next/static/chunks/webpack-*.js',
    '/_next/static/chunks/pages/_app-*.js',
    '/_next/static/chunks/pages/index-*.js',
    '/_next/static/css/*.css',
    '/_next/static/media/*',
    '/_next/image*'
  ],
  nuxt: [
    '/_nuxt/*.js',
    '/_nuxt/*.css',
    '/_nuxt/img/*',
    '/_nuxt/icons/*'
  ],
  generic: [
    '/js/*.js',
    '/css/*.css',
    '/assets/*',
    '/images/*',
    '/img/*',
    '/fonts/*',
    '/media/*',
    '/static/*',
    '/build/*',
    '/dist/*'
  ]
};

// Common file extensions to try
const commonExtensions = ['.js', '.css', '.html', '.json', '.xml', '.txt', '.ico', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.woff', '.woff2', '.ttf', '.eot'];

// HTTP request function with timeout and redirect handling
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname + parsedUrl.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        ...options.headers
      }
    };
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      // Handle redirects
      if (options.followRedirects && (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307 || res.statusCode === 308)) {
        const redirectUrl = res.headers.location;
        if (redirectUrl) {
          const fullRedirectUrl = redirectUrl.startsWith('http') ? redirectUrl : new URL(redirectUrl, url).href;
          return makeRequest(fullRedirectUrl, options).then(resolve).catch(reject);
        }
      }
      
      res.on('data', chunk => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', reject);
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Download file function
function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    fs.ensureFileSync(filePath);
    const file = fs.createWriteStream(filePath);
    
    const req = client.get(url, (response) => {
      if (response.statusCode === 404) {
        fs.unlinkSync(filePath);
        resolve({ success: false, status: 404 });
        return;
      }
      
      if (response.statusCode !== 200) {
        fs.unlinkSync(filePath);
        resolve({ success: false, status: response.statusCode });
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve({ success: true, status: 200, size: response.headers['content-length'] });
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {});
        reject(err);
      });
    });
    
    req.on('error', reject);
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Parse robots.txt for additional paths
async function parseRobotsTxt() {
  console.log('🤖 Checking robots.txt...');
  try {
    const response = await makeRequest(`${siteUrl}/robots.txt`);
    if (response.statusCode === 200) {
      const lines = response.data.split('\n');
      const paths = new Set();
      
      lines.forEach(line => {
        const trimmed = line.trim();
        if (trimmed.startsWith('Disallow:') || trimmed.startsWith('Allow:')) {
          const path = trimmed.split(':')[1].trim();
          if (path && path !== '/' && !path.includes('*')) {
            paths.add(path);
          }
        }
        if (trimmed.startsWith('Sitemap:')) {
          const sitemapUrl = trimmed.split(':').slice(1).join(':').trim();
          paths.add(sitemapUrl);
        }
      });
      
      console.log(`📋 Found ${paths.size} paths in robots.txt`);
      return Array.from(paths);
    }
  } catch (error) {
    console.log('⚠️  Could not fetch robots.txt:', error.message);
  }
  return [];
}

// Parse sitemap for URLs
async function parseSitemap(sitemapUrl) {
  console.log(`🗺️  Parsing sitemap: ${sitemapUrl}`);
  try {
    const response = await makeRequest(sitemapUrl);
    if (response.statusCode === 200) {
      const urls = new Set();
      
      // Extract URLs from XML sitemap
      const urlMatches = response.data.match(/<loc>([^<]+)<\/loc>/g);
      if (urlMatches) {
        urlMatches.forEach(match => {
          const url = match.replace(/<\/?loc>/g, '');
          if (url.startsWith(siteUrl)) {
            urls.add(url.replace(siteUrl, '') || '/');
          }
        });
      }
      
      // Extract sitemap references
      const sitemapMatches = response.data.match(/<sitemap>.*?<loc>([^<]+)<\/loc>.*?<\/sitemap>/gs);
      if (sitemapMatches) {
        for (const match of sitemapMatches) {
          const sitemapMatch = match.match(/<loc>([^<]+)<\/loc>/);
          if (sitemapMatch) {
            const nestedUrls = await parseSitemap(sitemapMatch[1]);
            nestedUrls.forEach(url => urls.add(url));
          }
        }
      }
      
      console.log(`📍 Found ${urls.size} URLs in sitemap`);
      return Array.from(urls);
    }
  } catch (error) {
    console.log(`⚠️  Could not parse sitemap ${sitemapUrl}:`, error.message);
  }
  return [];
}

// Extract links from HTML content
function extractLinksFromHtml(htmlContent, baseUrl) {
  const links = new Set();
  
  // Script tags
  const scriptRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
  let match;
  while ((match = scriptRegex.exec(htmlContent)) !== null) {
    links.add(match[1]);
  }
  
  // Link tags (CSS, etc.)
  const linkRegex = /<link[^>]*href=["']([^"']+)["'][^>]*>/gi;
  while ((match = linkRegex.exec(htmlContent)) !== null) {
    links.add(match[1]);
  }
  
  // Image tags (if including assets)
  if (options.includeAssets) {
    const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*>/gi;
    while ((match = imgRegex.exec(htmlContent)) !== null) {
      links.add(match[1]);
    }
  }
  
  // Anchor tags for additional pages
  const anchorRegex = /<a[^>]*href=["']([^"']+)["'][^>]*>/gi;
  while ((match = anchorRegex.exec(htmlContent)) !== null) {
    const href = match[1];
    if (!href.startsWith('#') && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
      links.add(href);
    }
  }
  
  // Convert relative URLs to absolute and filter
  return Array.from(links)
    .map(link => {
      if (link.startsWith('http')) return link;
      if (link.startsWith('//')) return 'https:' + link;
      if (link.startsWith('/')) return baseUrl + link;
      return baseUrl + '/' + link;
    })
    .filter(link => {
      try {
        const url = new URL(link);
        return url.hostname === new URL(baseUrl).hostname;
      } catch {
        return false;
      }
    });
}

// Generate potential file paths based on patterns
function generatePatternPaths() {
  const paths = new Set();
  
  options.patterns.forEach(pattern => {
    if (frameworkPatterns[pattern]) {
      frameworkPatterns[pattern].forEach(p => paths.add(p));
    }
  });
  
  // Add common paths
  const commonPaths = [
    '/',
    '/index.html',
    '/manifest.json',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    '/service-worker.js',
    '/sw.js'
  ];
  
  commonPaths.forEach(p => paths.add(p));
  
  return Array.from(paths);
}

// Intelligent path discovery
async function discoverPaths() {
  const allPaths = new Set();
  
  // 1. Add pattern-based paths
  console.log('🎯 Generating pattern-based paths...');
  const patternPaths = generatePatternPaths();
  patternPaths.forEach(p => allPaths.add(p));
  console.log(`📋 Generated ${patternPaths.length} pattern-based paths`);
  
  // 2. Parse robots.txt
  const robotsPaths = await parseRobotsTxt();
  robotsPaths.forEach(p => {
    if (p.startsWith('http')) {
      // It's a sitemap URL
      parseSitemap(p).then(urls => {
        urls.forEach(url => allPaths.add(url));
      });
    } else {
      allPaths.add(p);
    }
  });
  
  // 3. Try common sitemap locations
  const sitemapUrls = [
    `${siteUrl}/sitemap.xml`,
    `${siteUrl}/sitemap_index.xml`,
    `${siteUrl}/sitemaps.xml`
  ];
  
  for (const sitemapUrl of sitemapUrls) {
    const sitemapPaths = await parseSitemap(sitemapUrl);
    sitemapPaths.forEach(p => allPaths.add(p));
  }
  
  // 4. Deep discovery from HTML pages
  if (options.deep) {
    console.log('🔍 Starting deep discovery...');
    const htmlPaths = Array.from(allPaths).filter(p => p.endsWith('.html') || p === '/' || !p.includes('.'));
    
    for (const htmlPath of htmlPaths.slice(0, 10)) { // Limit to first 10 to avoid infinite loops
      try {
        const url = siteUrl + (htmlPath === '/' ? '' : htmlPath);
        const response = await makeRequest(url);
        
        if (response.statusCode === 200 && response.headers['content-type']?.includes('text/html')) {
          const discoveredLinks = extractLinksFromHtml(response.data, siteUrl);
          discoveredLinks.forEach(link => {
            if (link.startsWith(siteUrl)) {
              const relativePath = link.replace(siteUrl, '') || '/';
              allPaths.add(relativePath);
            }
          });
          console.log(`🔗 Discovered ${discoveredLinks.length} links from ${htmlPath}`);
        }
      } catch (error) {
        console.log(`⚠️  Failed to analyze ${htmlPath}:`, error.message);
      }
    }
  }
  
  return Array.from(allPaths);
}

// Main scraping function
async function scrapeWebsite() {
  try {
    console.log('🔍 Discovering paths...');
    const pathsToTry = await discoverPaths();
    console.log(`📋 Total paths to try: ${pathsToTry.length}`);
    
    console.log('\n📥 Starting downloads...');
    
    const MAX_CONCURRENT = 10;
    let inProgress = 0;
    let completed = 0;
    let failed = 0;
    
    async function downloadNext() {
      if (pathsToTry.length === 0) return;
      
      const filePath = pathsToTry.shift();
      const url = siteUrl + (filePath === '/' ? '' : filePath);
      let localPath = path.join(outputDir, filePath === '/' ? 'index.html' : filePath.substring(1));
      
      // Skip if already downloaded
      if (downloadedFiles.has(filePath)) {
        return downloadNext();
      }
      
      inProgress++;
      const progress = `[${completed + failed + 1}/${completed + failed + inProgress + pathsToTry.length}]`;
      console.log(`${progress} Trying: ${filePath}`);
      
      try {
        const result = await downloadFile(url, localPath);
        if (result.success) {
          completed++;
          downloadedFiles.add(filePath);
          const size = result.size ? ` (${Math.round(result.size / 1024)}KB)` : '';
          console.log(`✅ ${progress} Downloaded: ${filePath}${size}`);
        } else {
          failed++;
          failedUrls.add(filePath);
          if (result.status !== 404) {
            console.log(`⚠️  ${progress} Failed (${result.status}): ${filePath}`);
          }
        }
      } catch (error) {
        failed++;
        failedUrls.add(filePath);
        console.log(`❌ ${progress} Error: ${filePath} - ${error.message}`);
      }
      
      inProgress--;
      
      if (pathsToTry.length > 0) {
        await downloadNext();
      }
    }
    
    // Start concurrent downloads
    const promises = [];
    for (let i = 0; i < Math.min(MAX_CONCURRENT, pathsToTry.length); i++) {
      promises.push(downloadNext());
    }
    
    await Promise.all(promises);
    
    // Generate report
    console.log('\n' + '='.repeat(50));
    console.log('🎉 SCRAPING COMPLETE!');
    console.log(`📊 Statistics:`);
    console.log(`   ✅ Successfully downloaded: ${completed} files`);
    console.log(`   ❌ Failed/Not found: ${failed} files`);
    console.log(`   📁 Output directory: ${outputDir}`);
    
    // Create detailed report
    const report = {
      scrapingDate: new Date().toISOString(),
      siteUrl,
      options,
      statistics: {
        totalAttempted: completed + failed,
        successful: completed,
        failed: failed
      },
      downloadedFiles: Array.from(downloadedFiles),
      failedUrls: Array.from(failedUrls)
    };
    
    fs.writeFileSync(
      path.join(outputDir, 'scraping-report.json'), 
      JSON.stringify(report, null, 2)
    );
    
    console.log('\n📄 Detailed report saved to scraping-report.json');
    
    if (completed > 0) {
      console.log('\n📋 Files downloaded successfully! Check the output directory.');
    } else {
      console.log('\n⚠️  No files were downloaded. Try different options or check the site URL.');
    }
    
  } catch (error) {
    console.error('❌ Scraping failed:', error.stack);
    process.exit(1);
  }
}

// Run the scraper
scrapeWebsite();