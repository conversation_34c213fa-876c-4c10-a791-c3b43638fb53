#!/usr/bin/env node

const requireAuth = require('firebase-tools/lib/requireAuth');
const api = require('firebase-tools/lib/api');
const fs = require('fs-extra');
const https = require('https');
const http = require('http');
const { URL } = require('url');
const path = require('path');

if (!process.argv[2]) {
    console.error(`
ERROR: Must supply a site name or URL. Usage:

  node comprehensive-firebase-recovery.js <site_name_or_url> [options]
  
Examples:
  node comprehensive-firebase-recovery.js my-site
  node comprehensive-firebase-recovery.js https://my-site.firebaseapp.com
  node comprehensive-firebase-recovery.js my-site --method=all
  node comprehensive-firebase-recovery.js my-site --method=firebase
  node comprehensive-firebase-recovery.js https://my-site.web.app --method=scrape

Options:
  --method=all        Use all recovery methods (default)
  --method=firebase   Use Firebase API only (requires auth)
  --method=scrape     Use web scraping only
  --deep             Enable deep discovery mode
  --source-maps      Attempt to recover source maps
`);
  process.exit(1);
}

const input = process.argv[2];
const options = {
  method: 'all',
  deep: false,
  sourceMaps: false
};

// Parse command line options
for (let i = 3; i < process.argv.length; i++) {
  const arg = process.argv[i];
  if (arg.startsWith('--method=')) {
    options.method = arg.split('=')[1];
  } else if (arg === '--deep') {
    options.deep = true;
  } else if (arg === '--source-maps') {
    options.sourceMaps = true;
  }
}

// Determine if input is URL or site name
let siteUrl, siteName;
if (input.startsWith('http')) {
  siteUrl = input.replace(/\/$/, '');
  siteName = new URL(siteUrl).hostname.split('.')[0];
} else {
  siteName = input;
  siteUrl = `https://${siteName}.firebaseapp.com`;
}

const outputDir = `${siteName}_recovered_${Date.now()}`;

console.log(`🔥 Firebase Code Recovery Tool`);
console.log(`📁 Site: ${siteName}`);
console.log(`🌐 URL: ${siteUrl}`);
console.log(`📂 Output: ${outputDir}`);
console.log(`⚙️  Method: ${options.method}`);
console.log(`🔍 Deep mode: ${options.deep}`);
console.log(`🗺️  Source maps: ${options.sourceMaps}`);
console.log('\n' + '='.repeat(50) + '\n');

// Firebase API methods
async function getLatestVersionName() {
  try {
    const result = await api.request('GET', `/v1beta1/sites/${siteName}/releases?pageSize=1`, {
      auth: true,
      origin: api.hostingApiOrigin,
    });
    const release = (result.body.releases || [])[0];
    if (release) {
      return release.version.name;
    }
  } catch (error) {
    console.log('⚠️  Firebase API access failed:', error.message);
  }
  return null;
}

const LIST_PAGE_SIZE = 1000;
async function listFirebaseFiles(versionName, existing = [], pageToken = null) {
  const result = await api.request('GET', `/v1beta1/${versionName}/files?pageSize=${LIST_PAGE_SIZE}${pageToken ? `&pageToken=${pageToken}` : ''}`, {
    auth: true, 
    origin: api.hostingApiOrigin
  });
  result.body.files.forEach(file => existing.push(file.path));
  if (result.body.nextPageToken) {
    return await listFirebaseFiles(versionName, existing, result.body.nextPageToken);
  }
  return existing;
}

// Web scraping methods
function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    fs.ensureFileSync(filePath);
    const file = fs.createWriteStream(filePath);
    
    const request = client.get(url, (response) => {
      if (response.statusCode === 404) {
        fs.unlinkSync(filePath);
        resolve({ success: false, status: 404 });
        return;
      }
      
      if (response.statusCode !== 200) {
        fs.unlinkSync(filePath);
        resolve({ success: false, status: response.statusCode });
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve({ success: true, status: 200 });
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {});
        reject(err);
      });
    });
    
    request.on('error', (err) => {
      reject(err);
    });
    
    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Common file patterns to try
const commonFiles = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/service-worker.js',
  '/sw.js',
  
  // React/Create React App
  '/static/js/main.js',
  '/static/css/main.css',
  '/static/js/runtime-main.js',
  '/static/js/chunk.js',
  '/static/media/',
  
  // Next.js
  '/_next/static/chunks/main.js',
  '/_next/static/chunks/pages/_app.js',
  '/_next/static/chunks/pages/index.js',
  '/_next/static/css/app.css',
  '/_next/static/webpack/',
  
  // Vue.js
  '/js/app.js',
  '/js/chunk-vendors.js',
  '/css/app.css',
  
  // Angular
  '/main.js',
  '/polyfills.js',
  '/runtime.js',
  '/vendor.js',
  '/styles.css',
  
  // Generic
  '/js/main.js',
  '/js/script.js',
  '/js/app.js',
  '/css/main.css',
  '/css/style.css',
  '/css/app.css',
  '/assets/index.js',
  '/assets/index.css',
  '/build/static/js/main.js',
  '/build/static/css/main.css',
  
  // API routes
  '/api/',
  '/.well-known/assetlinks.json',
  '/.well-known/apple-app-site-association'
];

// Extract links from HTML content
function extractLinksFromHtml(htmlContent, baseUrl) {
  const links = new Set();
  
  // Script tags
  const scriptRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
  let match;
  while ((match = scriptRegex.exec(htmlContent)) !== null) {
    links.add(match[1]);
  }
  
  // Link tags (CSS, etc.)
  const linkRegex = /<link[^>]*href=["']([^"']+)["'][^>]*>/gi;
  while ((match = linkRegex.exec(htmlContent)) !== null) {
    links.add(match[1]);
  }
  
  // Image tags
  const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*>/gi;
  while ((match = imgRegex.exec(htmlContent)) !== null) {
    links.add(match[1]);
  }
  
  // Convert relative URLs to absolute
  return Array.from(links).map(link => {
    if (link.startsWith('http')) return link;
    if (link.startsWith('//')) return 'https:' + link;
    if (link.startsWith('/')) return baseUrl + link;
    return baseUrl + '/' + link;
  });
}

// Main recovery function
async function recoverCode() {
  let allFiles = new Set();
  let downloadedCount = 0;
  
  try {
    // Method 1: Firebase API (if available)
    if (options.method === 'all' || options.method === 'firebase') {
      console.log('🔥 Attempting Firebase API method...');
      try {
        await requireAuth({}, ['https://www.googleapis.com/auth/cloud-platform']);
        const versionName = await getLatestVersionName();
        
        if (versionName) {
          console.log('✅ Firebase API authenticated successfully');
          const firebaseFiles = await listFirebaseFiles(versionName);
          console.log(`📋 Found ${firebaseFiles.length} files via Firebase API`);
          
          firebaseFiles.forEach(file => allFiles.add(file));
        }
      } catch (error) {
        console.log('⚠️  Firebase API method failed:', error.message);
        if (options.method === 'firebase') {
          console.log('❌ Firebase-only mode failed. Try --method=scrape or --method=all');
          process.exit(1);
        }
      }
    }
    
    // Method 2: Web scraping
    if (options.method === 'all' || options.method === 'scrape') {
      console.log('🕷️  Starting web scraping method...');
      
      // Try common files
      console.log('📁 Checking common file patterns...');
      for (const filePath of commonFiles) {
        allFiles.add(filePath);
      }
      
      // Download and analyze HTML files for more links
      if (options.deep) {
        console.log('🔍 Deep discovery mode enabled...');
        const htmlFiles = Array.from(allFiles).filter(f => f.endsWith('.html') || f === '/');
        
        for (const htmlFile of htmlFiles) {
          try {
            const url = siteUrl + (htmlFile === '/' ? '' : htmlFile);
            const tempPath = path.join(outputDir, 'temp_' + Math.random().toString(36).substr(2, 9) + '.html');
            
            const result = await downloadFile(url, tempPath);
            if (result.success) {
              const content = fs.readFileSync(tempPath, 'utf8');
              const discoveredLinks = extractLinksFromHtml(content, siteUrl);
              
              discoveredLinks.forEach(link => {
                if (link.startsWith(siteUrl)) {
                  const relativePath = link.replace(siteUrl, '') || '/';
                  allFiles.add(relativePath);
                }
              });
              
              fs.unlinkSync(tempPath);
              console.log(`🔗 Discovered ${discoveredLinks.length} additional links from ${htmlFile}`);
            }
          } catch (error) {
            console.log(`⚠️  Failed to analyze ${htmlFile}:`, error.message);
          }
        }
      }
    }
    
    // Download all discovered files
    const filesToDownload = Array.from(allFiles);
    console.log(`\n📥 Starting download of ${filesToDownload.length} files...`);
    
    const MAX_CONCURRENT = 10;
    let inProgress = 0;
    let completed = 0;
    let failed = 0;
    
    async function downloadNext() {
      if (filesToDownload.length === 0) return;
      
      const filePath = filesToDownload.shift();
      const url = siteUrl + (filePath === '/' ? '' : filePath);
      let localPath = path.join(outputDir, filePath === '/' ? 'index.html' : filePath.substring(1));
      
      inProgress++;
      const progress = `[${completed + failed + 1}/${completed + failed + inProgress + filesToDownload.length}]`;
      console.log(`${progress} Downloading: ${filePath}`);
      
      try {
        const result = await downloadFile(url, localPath);
        if (result.success) {
          completed++;
          downloadedCount++;
          console.log(`✅ ${progress} Downloaded: ${filePath}`);
          
          // Check for source maps
          if (options.sourceMaps && (filePath.endsWith('.js') || filePath.endsWith('.css'))) {
            try {
              const content = fs.readFileSync(localPath, 'utf8');
              const sourceMapMatch = content.match(/\/\/#\s*sourceMappingURL=(.+)/);
              if (sourceMapMatch) {
                const mapUrl = sourceMapMatch[1].startsWith('http') ? 
                  sourceMapMatch[1] : 
                  siteUrl + path.dirname(filePath) + '/' + sourceMapMatch[1];
                
                const mapPath = localPath + '.map';
                const mapResult = await downloadFile(mapUrl, mapPath);
                if (mapResult.success) {
                  console.log(`🗺️  Downloaded source map: ${filePath}.map`);
                }
              }
            } catch (error) {
              // Ignore source map errors
            }
          }
        } else {
          failed++;
          if (result.status !== 404) {
            console.log(`⚠️  ${progress} Failed (${result.status}): ${filePath}`);
          }
        }
      } catch (error) {
        failed++;
        console.log(`❌ ${progress} Error: ${filePath} - ${error.message}`);
      }
      
      inProgress--;
      
      if (filesToDownload.length > 0) {
        await downloadNext();
      }
    }
    
    // Start concurrent downloads
    const promises = [];
    for (let i = 0; i < Math.min(MAX_CONCURRENT, filesToDownload.length); i++) {
      promises.push(downloadNext());
    }
    
    await Promise.all(promises);
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 RECOVERY COMPLETE!');
    console.log(`📊 Statistics:`);
    console.log(`   ✅ Successfully downloaded: ${completed} files`);
    console.log(`   ❌ Failed/Not found: ${failed} files`);
    console.log(`   📁 Output directory: ${outputDir}`);
    
    if (completed > 0) {
      console.log('\n📋 Next steps:');
      console.log('1. Check the downloaded files in the output directory');
      console.log('2. Look for main application files (index.html, main.js, etc.)');
      console.log('3. Check source maps (.map files) for original source code');
      console.log('4. Examine package.json or similar files for dependencies');
      
      // Create a summary file
      const summary = {
        recoveryDate: new Date().toISOString(),
        siteName,
        siteUrl,
        method: options.method,
        options,
        statistics: {
          totalAttempted: completed + failed,
          successful: completed,
          failed: failed
        },
        downloadedFiles: fs.readdirSync(outputDir).filter(f => !f.startsWith('temp_'))
      };
      
      fs.writeFileSync(
        path.join(outputDir, 'recovery-summary.json'), 
        JSON.stringify(summary, null, 2)
      );
      
      console.log('\n📄 Recovery summary saved to recovery-summary.json');
    } else {
      console.log('\n⚠️  No files were successfully downloaded. This could mean:');
      console.log('   - The site URL is incorrect');
      console.log('   - The site is not publicly accessible');
      console.log('   - The site uses different file structures');
      console.log('   - Try different recovery methods or options');
    }
    
  } catch (error) {
    console.error('❌ Recovery failed:', error.stack);
    process.exit(1);
  }
}

// Run the recovery
recoverCode();