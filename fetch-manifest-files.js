#!/usr/bin/env node

const https = require('https');
const fs = require('fs-extra');

const siteUrl = 'https://windowsassistant-1871a.web.app';
const dirName = 'windowsassistant-1871a_web_app_simple_fetch';

// Files from asset-manifest.json
const manifestFiles = [
  '/static/js/453.1b8bd27e.chunk.js',
  '/static/css/main.4943d850.css.map',
  '/static/js/main.168e96bf.js.map',
  '/static/js/453.1b8bd27e.chunk.js.map'
];

function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    fs.ensureFileSync(filePath);
    const file = fs.createWriteStream(filePath);
    
    https.get(url, (response) => {
      if (response.statusCode === 404) {
        fs.unlinkSync(filePath);
        resolve(false);
        return;
      }
      
      if (response.statusCode !== 200) {
        fs.unlinkSync(filePath);
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve(true);
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {});
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

(async function() {
  try {
    console.log(`Downloading manifest files to ${dirName}/`);
    
    let downloaded = 0;
    
    for (const filePath of manifestFiles) {
      const url = `${siteUrl}${filePath}`;
      const localPath = `${dirName}${filePath}`;
      
      console.log(`Trying: ${filePath}`);
      
      try {
        const success = await downloadFile(url, localPath);
        if (success) {
          console.log(`✓ Downloaded: ${filePath}`);
          downloaded++;
        } else {
          console.log(`- Not found: ${filePath}`);
        }
      } catch (error) {
        console.log(`✗ Error downloading ${filePath}:`, error.message);
      }
    }
    
    console.log();
    console.log(`✅ Downloaded ${downloaded} manifest files`);
    
  } catch (e) {
    console.error("ERROR:", e.stack);
    process.exit(1);
  }
})();
